import { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import DashboardLayout from '@/components/layout/DashboardLayout';
import JobFormComponent from '@/components/jobs/JobForm';
import { Job } from '@/types/job';
import { Client } from '@/types/client';
import { jobsApi, clientsApi } from '@/services/apiService';

export default function JobForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const [job, setJob] = useState<Job | undefined>(undefined);
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoadingJob, setIsLoadingJob] = useState<boolean>(false);
  const [isLoadingClients, setIsLoadingClients] = useState<boolean>(false);
  const [preselectedClient, setPreselectedClient] = useState<Client | null>(null);

  const isEditMode = !!id;

  // Estado para el guardado
  const [isSaving, setIsSaving] = useState(false);

  // Extract client ID from URL parameters
  const clientIdFromUrl = searchParams.get('client');

  // Load job data for editing
  useEffect(() => {
    const loadJob = async () => {
      if (isEditMode && id) {
        setIsLoadingJob(true);
        try {
          const jobData = await jobsApi.getById(id);
          setJob(jobData);
        } catch (error) {
          console.error('Error loading job:', error);
          toast({
            title: 'Error',
            description: 'Failed to load job data. Please try again.',
            variant: 'destructive',
          });
          navigate('/jobs');
        } finally {
          setIsLoadingJob(false);
        }
      }
    };

    loadJob();
  }, [id, isEditMode, toast, navigate]);

  // Load clients data and handle preselected client
  useEffect(() => {
    const loadClients = async () => {
      setIsLoadingClients(true);
      try {
        const clientsData = await clientsApi.getAll();
        setClients(clientsData);

        // If there's a client ID in the URL, find and set the preselected client
        if (clientIdFromUrl && !isEditMode) {
          const selectedClient = clientsData.find(client => client.id === clientIdFromUrl);
          if (selectedClient) {
            setPreselectedClient(selectedClient);
          } else {
            // Client ID in URL doesn't exist, show warning
            toast({
              title: 'Warning',
              description: 'The specified client was not found. Please select a client manually.',
              variant: 'destructive',
            });
          }
        }
      } catch (error) {
        console.error('Error loading clients:', error);
        toast({
          title: 'Error',
          description: 'Failed to load clients data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingClients(false);
      }
    };

    loadClients();
  }, [toast, clientIdFromUrl, isEditMode]);



  // Handle form submission
  const handleSubmit = async (data: any) => {
    setIsSaving(true);
    try {
      if (isEditMode && id) {
        await jobsApi.update(id, data);
        toast({
          title: 'Success',
          description: 'Job updated successfully',
        });
      } else {
        await jobsApi.create(data);
        toast({
          title: 'Success',
          description: 'Job created successfully',
        });
      }

      navigate('/jobs');
    } catch (error) {
      console.error('Error saving job:', error);
      toast({
        title: 'Error',
        description: 'Failed to save job. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditMode ? 'Edit Job' : 'Add New Job'}
        </h1>
      </div>

      <JobFormComponent
        job={job}
        clients={clients}
        onSubmit={handleSubmit}
        isLoading={isLoadingJob || isLoadingClients || isSaving}
        preselectedClientId={preselectedClient?.id}
        isClientLocked={!!preselectedClient && !isEditMode}
      />
    </DashboardLayout>
  );
}
