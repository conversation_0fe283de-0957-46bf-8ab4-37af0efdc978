import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Job, JobStatus, JobType } from '@/types/job';
import { Client } from '@/types/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus } from 'lucide-react';
import { ClientSelectionModal } from './ClientSelectionModal';

// Esquema de validación para el formulario
const jobFormSchema = z.object({
  title: z.string().min(2, 'Title must be at least 2 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  status: z.enum(['open', 'closed', 'draft', 'archived']),
  type: z.enum(['full-time', 'part-time', 'contract', 'internship', 'remote']),
  location: z.string().min(2, 'Location must be at least 2 characters'),
  salary_range: z.string().optional(),
  department: z.string().optional(),
  requirements: z.string().optional(),
  responsibilities: z.string().optional(),
  closing_date: z.string().optional(),
  client_id: z.string().min(1, 'Please select a client').refine(val => val !== 'none' && val !== 'no-clients', {
    message: 'Please select a client'
  })
});

type JobFormValues = z.infer<typeof jobFormSchema>;

interface JobFormProps {
  job?: Job;
  clients?: Client[];
  onSubmit: (data: JobFormValues) => void;
  isLoading?: boolean;
  preselectedClientId?: string;
  isClientLocked?: boolean;
}

export default function JobForm({
  job,
  clients = [],
  onSubmit,
  isLoading = false,
  preselectedClientId,
  isClientLocked = false
}: JobFormProps) {
  const [formError, setFormError] = useState<string | null>(null);
  const [showClientModal, setShowClientModal] = useState(false);

  // Inicializar el formulario con valores por defecto o del trabajo existente
  const form = useForm<JobFormValues>({
    resolver: zodResolver(jobFormSchema),
    defaultValues: {
      title: job?.title || '',
      description: job?.description || '',
      status: job?.status || 'draft',
      type: job?.type || 'full-time',
      location: job?.location || '',
      salary_range: job?.salary_range || '',
      department: job?.department || '',
      requirements: job?.requirements ? job.requirements.join('\n') : '',
      responsibilities: job?.responsibilities ? job.responsibilities.join('\n') : '',
      closing_date: job?.closing_date || '',
      client_id: job?.client_id || preselectedClientId || ''
    },
  });

  // Manejar el envío del formulario
  const handleSubmit = async (data: JobFormValues) => {
    try {
      setFormError(null);

      // Convertir requirements y responsibilities de string a array
      const formattedData = {
        ...data,
        requirements: data.requirements ? data.requirements.split('\n').filter(item => item.trim() !== '') : undefined,
        responsibilities: data.responsibilities ? data.responsibilities.split('\n').filter(item => item.trim() !== '') : undefined,
        client_id: data.client_id
      };

      onSubmit(formattedData);
    } catch (error) {
      console.error('Error submitting job form:', error);
      setFormError('An error occurred while saving the job. Please try again.');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{job ? 'Edit Job' : 'Add New Job'}</CardTitle>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardContent className="space-y-6">
            {formError && (
              <div className="bg-destructive/15 text-destructive p-3 rounded-md text-sm">
                {formError}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Frontend Developer" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input placeholder="New York, NY or Remote" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="open">Open</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select job type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="full-time">Full-time</SelectItem>
                        <SelectItem value="part-time">Part-time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="internship">Internship</SelectItem>
                        <SelectItem value="remote">Remote</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="salary_range"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Salary Range</FormLabel>
                    <FormControl>
                      <Input placeholder="$80,000 - $100,000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      <Input placeholder="Engineering" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="closing_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Closing Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="client_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client *</FormLabel>
                    <div className="flex gap-2">
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isClientLocked}
                      >
                        <FormControl>
                          <SelectTrigger className={`flex-1 ${isClientLocked ? 'opacity-60 cursor-not-allowed' : ''}`}>
                            <SelectValue placeholder="Select a client" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {clients.length === 0 ? (
                            <SelectItem value="no-clients" disabled>
                              No clients available
                            </SelectItem>
                          ) : (
                            clients.map((client) => (
                              <SelectItem key={client.id} value={client.id}>
                                {client.company_name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => setShowClientModal(true)}
                        disabled={isClientLocked}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <FormDescription data-testid="client-field-description">
                      {isClientLocked
                        ? 'Client is pre-selected from the client details page and cannot be changed.'
                        : 'Select the client this job is for. You can create a new client by clicking the + button.'
                      }
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Detailed description of the job..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="requirements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Requirements</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter each requirement on a new line..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter each requirement on a new line
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="responsibilities"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Responsibilities</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter each responsibility on a new line..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter each responsibility on a new line
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex justify-end gap-2">
            <Button variant="outline" type="button" onClick={() => window.history.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : job ? 'Update Job' : 'Create Job'}
            </Button>
          </CardFooter>
        </form>
      </Form>

      <ClientSelectionModal
        isOpen={showClientModal}
        onClose={() => setShowClientModal(false)}
        onClientSelect={(clientId) => {
          form.setValue('client_id', clientId);
          setShowClientModal(false);
        }}
        selectedClientId={form.watch('client_id')}
      />
    </Card>
  );
}
